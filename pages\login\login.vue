<template>
	<view class="login-container">
		<!-- 顶部图片区域 -->
		<view class="header-image">
			<image
				src="./image.png"
				mode="aspectFill"
				class="header-bg-image"
			></image>
		</view>

		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- Logo和标题 -->
			
			<!-- 登录表单 -->
			<view class="login-form">
				<view v-if="loginType==1" class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">👤</text>
						<input
							v-model="username"
							class="form-input"
							type="text"
							placeholder="请输入账号"
							placeholder-class="placeholder-style"
						/>
					</view>
				</view>

				<view v-if="loginType==1" class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">🔒</text>
						<input
							v-model="password"
							class="form-input"
							type="password"
							placeholder="请输入密码"
							placeholder-class="placeholder-style"
						/>
					</view>
				</view>

				<view v-if="roleNum>1" class="form-item">
					<view class="input-wrapper picker-wrapper">
						<text class="input-icon">👥</text>
						<picker @change="optionsChange" :value="index" :range="options" class="form-picker">
							<view class="picker-text">{{options[index]}}</view>
						</picker>
					</view>
				</view>

				<!-- 登录按钮 -->
				<button
					v-if="loginType==1"
					class="login-btn"
					@tap="onLoginTap"
					type="primary"
				>
					登录
				</button>

				<button
					v-if="loginType==2"
					class="login-btn face-login-btn"
					@tap="onFaceLoginTap"
					type="primary"
				>
					人脸识别登录
				</button>

				<!-- 注册链接 -->
				<view class="register-link">
					<text class="register-text" @tap="onRegisterTap('yonghu')">
						还没有账号？立即注册
					</text>
				</view>
			</view>
		</view>

		<!-- 底部装饰 -->
		<view class="footer-decoration">
			<text class="footer-text">专业 · 贴心 · 安全</text>
		</view>
	</view>
</template>

<script>
	import menu from '@/utils/menu'
	export default {
		data() {
			return {
				username: '',
				password: '',
				loginType: 1,
				codes: [{
					num: 1,
					color: '#000',
					rotate: '10deg',
					size: '16px'
				}, {
					num: 2,
					color: '#000',
					rotate: '10deg',
					size: '16px'
				}, {
					num: 3,
					color: '#000',
					rotate: '10deg',
					size: '16px'
				}, {
					num: 4,
					color: '#000',
					rotate: '10deg',
					size: '16px'
				}],
				options: [
					'请选择登录用户类型',
				],
				optionsValues: [
					'',
					'yonghu',
				],
				index: 0,
				roleNum: 0,

			}
		},
		onLoad() {
			let options = ['请选择登录用户类型'];
			let menus = menu.list();
			this.menuList = menus;
			for (let i = 0; i < this.menuList.length; i++) {
				if (this.menuList[i].hasFrontLogin == '是') {
					options.push(this.menuList[i].roleName);
					this.roleNum++;
				}
			}
			if (this.roleNum == 1) {
				this.index = 1;
			}
			this.options = options;
			this.styleChange()
		},
		onShow() {},
		mounted() {},
		methods: {
			styleChange() {
				this.$nextTick(() => {
					// document.querySelectorAll('.uni-input .uni-input-input').forEach(el=>{
					//   el.style.backgroundColor = this.loginFrom.content.input.backgroundColor
					// })
				})
			},
			onRegisterTap(tableName) {
				uni.setStorageSync("loginTable", tableName);
				this.$utils.jump('../register/register')
			},
			async onLoginTap() {
				if (!this.username) {
					this.$utils.msg('请输入用户名')
					return
				}
				if (!this.password) {
					this.$utils.msg('请输入用户密码')
					return
				}
				if (!this.optionsValues[this.index]) {
					this.$utils.msg('请选择登录用户类型')
					return
				}

				this.loginPost()

			},
			async loginPost() {

				let res = await this.$api.login(`${this.optionsValues[this.index]}`, {
					username: this.username,
					password: this.password
				});
				uni.removeStorageSync("useridTag");
				uni.setStorageSync("appToken", res.token);
				uni.setStorageSync("nickname", this.username);
				uni.setStorageSync("nowTable", `${this.optionsValues[this.index]}`);
				res = await this.$api.session(`${this.optionsValues[this.index]}`);
				if (res.data.touxiang) {
					uni.setStorageSync('headportrait', res.data.touxiang);
				} else if (res.data.headportrait) {
					uni.setStorageSync('headportrait', res.data.headportrait);
				}
				uni.setStorageSync('userSession', JSON.stringify(res.data))
				// 保存用户id
				uni.setStorageSync("appUserid", res.data.id);
				if (res.data.vip) {
					uni.setStorageSync("vip", res.data.vip);
				}
				uni.setStorageSync("appRole", `${this.options[this.index]}`);
				this.$utils.tab('../index/index');
			},
			optionsChange(e) {
				this.index = e.target.value
			}
		}
	}
</script>

<style lang="scss" scoped>
	page {
		height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	.login-container {
		width: 100vw;
		height: 100vh;
		display: flex;
		flex-direction: column;
		background: linear-gradient(135deg, #4A90E2 0%, #357ABD 50%, #2E5984 100%);
		position: relative;
		overflow: hidden;
	}

	.header-image {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 45vh;
		z-index: 1;
		overflow: hidden;

		.header-bg-image {
			width: 100%;
			height: 80%;
			object-fit: cover;
		}

		/* 添加渐变遮罩，让图片与下方内容更好融合 */
		&::after {
			content: '';
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			height: 100rpx;
			background: linear-gradient(to bottom, transparent, rgba(74,144,226,0.3));
			pointer-events: none;
		}
	}

	.main-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		align-items: center;
		padding: 60rpx 80rpx 346rpx;
		z-index: 2;
		position: relative;
		margin-top: 36vh;
		background: linear-gradient(135deg, #4A90E2 0%, #357ABD 50%, #2E5984 100%);
	}

	.logo-section {
		text-align: center;
		margin-bottom: 60rpx;

		.logo-icon {
			width: 100rpx;
			height: 100rpx;
			background: rgba(255,255,255,0.15);
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 0 auto 30rpx;
			backdrop-filter: blur(10rpx);
			border: 2rpx solid rgba(255,255,255,0.2);

			.iconfont {
				font-size: 50rpx;
				color: #ffffff;
			}
		}

		.app-title {
			display: block;
			font-size: 42rpx;
			font-weight: 600;
			color: #ffffff;
			margin-bottom: 12rpx;
			text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
		}

		.app-subtitle {
			display: block;
			font-size: 26rpx;
			color: rgba(255,255,255,0.8);
			font-weight: 300;
		}
	}

	.login-form {
		width: 100%;
		max-width: 600rpx;

		.form-item {
			margin-bottom: 40rpx;

			.input-wrapper {
				position: relative;
				background: rgba(255,255,255,0.95);
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				padding: 0 30rpx;
				box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
				backdrop-filter: blur(10rpx);
				border: 1rpx solid rgba(255,255,255,0.2);

				.input-icon {
					font-size: 32rpx;
					margin-right: 20rpx;
					color: #4A90E2;
				}

				.form-input {
					flex: 1;
					height: 88rpx;
					font-size: 32rpx;
					color: #333333;
					background: transparent;
					border: none;
				}

				.placeholder-style {
					color: #999999;
					font-size: 30rpx;
				}
			}

			.picker-wrapper {
				.form-picker {
					flex: 1;
					height: 88rpx;
					display: flex;
					align-items: center;

					.picker-text {
						font-size: 32rpx;
						color: #333333;
					}
				}
			}
		}

		.login-btn {
			width: 100%;
			height: 96rpx;
			background: linear-gradient(135deg, #FF6B6B 0%, #FF5252 100%);
			border: none;
			border-radius: 16rpx;
			font-size: 36rpx;
			font-weight: 600;
			color: #ffffff;
			margin: 60rpx 0 40rpx;
			box-shadow: 0 8rpx 24rpx rgba(255,107,107,0.3);
			transition: all 0.3s ease;

			&:active {
				transform: translateY(2rpx);
				box-shadow: 0 4rpx 12rpx rgba(255,107,107,0.3);
			}
		}

		.face-login-btn {
			background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
			box-shadow: 0 8rpx 24rpx rgba(76,175,80,0.3);

			&:active {
				box-shadow: 0 4rpx 12rpx rgba(76,175,80,0.3);
			}
		}

		.register-link {
			text-align: center;
			margin-top: 40rpx;

			.register-text {
				font-size: 28rpx;
				color: rgba(255,255,255,0.8);
				text-decoration: underline;
				text-underline-offset: 4rpx;
			}
		}
	}

	.footer-decoration {
		position: absolute;
		bottom: 120rpx;
		left: 0;
		right: 0;
		text-align: center;
		z-index: 2;

		.footer-text {
			font-size: 24rpx;
			color: rgba(255,255,255,0.6);
			letter-spacing: 4rpx;
		}
	}

	/* 响应式适配 */
	@media screen and (max-height: 1200rpx) {
		.main-content {
			padding: 40rpx 60rpx;
		}

		.logo-section {
			margin-bottom: 60rpx;

			.app-title {
				font-size: 42rpx;
			}
		}
	}
</style>